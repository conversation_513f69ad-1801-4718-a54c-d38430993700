<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>接口测试</el-breadcrumb-item>
      <el-breadcrumb-item>场景管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-input placeholder="请输入场景ID"
                    v-model="queryInfo.search_scene_id"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="4">
          <el-input placeholder="请输入场景名称"
                    v-model="queryInfo.search_scene_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="请输入案例ID"
                    v-model="queryInfo.case_id"
                    clearable>
          </el-input>
          </el-col>
        <el-col :span="3">
          <el-select placeholder="是否被引用"
                     v-model="queryInfo.is_related"
                     clearable>
            <el-option value="1" label="是"></el-option>
            <el-option value="0" label="否"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="请选择业务线"
                     v-model="queryInfo.business"
                     clearable
                     filterable>
            <el-option v-for="item in businessList"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryScene">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, 0, 'add')">添加场景</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="sceneList" border>
          <el-table-column label="场景ID" prop="scene_id" min-width="50px"></el-table-column>
          <el-table-column label="场景名称" prop="scene_name" min-width="160px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false,slotProps.row.scene_id, 'update')">{{ slotProps.row.scene_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row.scene_id, 'check')">{{ slotProps.row.scene_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="场景描述" prop="scene_describe" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属业务" prop="businessName" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="前置数据" prop="before_param" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="是否被引用" prop="is_related" show-overflow-tooltip min-width="50px">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.is_related===1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="190px">
            <template v-slot="slotProps">
            <el-button type="primary" icon="el-icon-edit" @click="showDialog(false,slotProps.row.scene_id, 'update')" size="mini"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0"  icon="el-icon-delete" size="mini" @click="removeSceneById(slotProps.row.scene_id)"></el-button>
            <el-tooltip class="item" effect="dark" content="复制场景" placement="top" :enterable="false">
              <el-button type="info" icon="el-icon-document-copy" size="mini" @click="copyScene(slotProps.row.scene_id)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="调试场景" placement="top" :enterable="false">
              <el-button type="success" icon="el-icon-caret-right" size="mini" @click="showSceneRunDialog(slotProps.row.scene_id)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="关联任务" placement="top" :enterable="false" >
              <el-button icon="el-icon-paperclip" type="warning" size="mini" @click="showTaskList(slotProps.row.scene_id,slotProps.row.scene_name)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="调试日志" placement="top" :enterable="false" >
              <el-button icon="el-icon-message" size="mini" @click="getSceneDebugLog(slotProps.row.scene_id)"></el-button>
            </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑抽屉 -->
    <el-drawer :visible.sync="dialogVisible"
               :title="title"
               direction="btt"
               size="90%"
               class="scene-drawer"
               @close="sceneDrawerClosed"
               v-loading="addLoading"
               @opened="addDrawerOpened">
      <el-scrollbar class="scene-drawer-content">
        <el-form :model="sceneForm"
                label-width="100px"
                :rules="sceneFormRules"
                ref="sceneFormRef">
          <el-divider content-position="left">基本信息</el-divider>
          <el-row>
            <el-col :span="12">
              <el-form-item label="场景名称" prop="scene_name">
                <el-input v-model="sceneForm.scene_name">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属业务" prop="business_id">
                <el-select v-model="sceneForm.business_id" filterable placeholder="请选择">
                  <el-option v-for="item in businessList"
                             :key="item.id"
                             :label="item.label"
                             :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="场景描述" prop="scene_describe">
                <el-input v-model="sceneForm.scene_describe">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="left">前置参数</el-divider>
          <el-row>
              <el-col :span="12">
                <el-form-item label="是否展示">
                  <el-switch
                    v-model="sceneForm.isShowPreparam"
                    active-color="#13ce66">
                  </el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="sceneForm.isShowPreparam">
                  <el-button type="primary" @click="addPreparam" size="small">添加参数</el-button>
                </el-form-item>
              </el-col>
          </el-row>
          <el-row v-if="sceneForm.isShowPreparam" :gutter="25">
            <template v-for="(item, index) in preparamList">
              <el-col :span="12" :key="index">
                <el-form-item :label="'参数' + (index + 1)">
                  <el-col :span="8">
                  <el-input type="text" v-model="item.paramName" />
                  </el-col>
                  <el-col :span="2">
                    <span>=</span>
                  </el-col>
                  <el-col :span="8">
                  <el-input type="text" v-model="item.paramValue" />
                  </el-col>
                  <el-col :span="2">
                    <i class="el-icon-delete" @click="deletePreparm(index)"></i>
                  </el-col>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <el-divider content-position="left">案例组成</el-divider>
          <el-row>
            <el-button size="small" @click="addCaseDrawerShow(9999)">添加案例</el-button>
            <span style="font-size:12px;margin-left:10px;color:red;">拖拽表格行可进行排序</span>
          </el-row>
          <div class="relationCaseTabel">
            <el-table :data="sceneForm.relation"
                      id="crTable"
                      row-key="counterKey">
              <el-table-column type="index" />
              <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
              <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
              <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
              <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
                <template v-slot="slotProps">
                  <span v-if="slotProps.row.is_get_param">是</span>
                  <span v-else>否</span>
                </template>
              </el-table-column>
              <el-table-column prop="response_param" min-width="100px" label="返回参数" />
              <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
              <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
                <template v-slot="slotProps">
                  <span v-if="slotProps.row.reset_param">是</span>
                  <span v-else>否</span>
                </template>
              </el-table-column>
              <el-table-column prop="instructions" min-width="100px" label="备注说明" show-overflow-tooltip />
              <el-table-column prop="is_check" min-width="100px" label="是否数据越权">
                <template v-slot="slotProps">
                  <el-switch
                    v-model="slotProps.row.is_check"
                    @change="changeIsCheck(slotProps.row)"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column min-width="170px" label="操作">
                <template v-slot="slotProps">
                  <el-button type="text" @click="editCaseDialogShow(slotProps.row, slotProps.$index)">编辑</el-button>
                  <el-button type="text" @click="addCaseDrawerShow(slotProps.$index)">添加</el-button>
                  <el-button type="text" @click="copyCase(slotProps.row, slotProps.$index)">复制</el-button>
                  <el-button type="text" @click="deleteCase(slotProps.$index)">删除</el-button>
                  <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
                    <span v-if="slotProps.row.is_enable">禁用</span>
                    <span v-else style="color:red">启用</span>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </el-scrollbar>
      <div class="scene-drawer-footer" v-if="type!='check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="success" @click="submitScene(false)" v-if="type=='update'">保 存</el-button>
        <el-button type="primary" @click="submitScene(true)">保存并关闭</el-button>
        <el-button type="warning" @click="showSceneRunDialog(sceneForm.scene_id)" v-if="type=='update'">运 行</el-button>
      </div>
    </el-drawer>
    <!-- 添加案例抽屉 -->
    <el-drawer :visible.sync="addCasedialogVisible"
               title="添加案例"
               direction="rtl"
               size="70%"
               class="add-case-drawer"
               @close="addCasedialogClosed">
      <el-scrollbar class="add-case-drawer-content">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-input placeholder="请输入案例ID"
                      v-model="caseQueryInfo.search_case_id"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="请输入案例名称"
                      v-model="caseQueryInfo.search_case_name"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="请输入接口地址"
                      v-model="caseQueryInfo.search_case_uri"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="caseQueryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
          <el-col :span="3">
            <el-button type="primary" @click="queryCase">查 询</el-button>
          </el-col>
        </el-row>
        <el-row>
        <el-tabs v-model="caseQueryInfo.type"
                 class="add"
                 @tab-click="getCaseList">
          <el-tab-pane label="接口案例" name="API">
          </el-tab-pane>
          <el-tab-pane label="SQL案例" name="SQL">
          </el-tab-pane>
          <el-tab-pane label="Redis案例" name="REDIS">
          </el-tab-pane>
          <el-tab-pane label="导入/导出解析案例" name="EXPORT">
          </el-tab-pane>
        </el-tabs>
      </el-row>
        <el-row>
        <el-table :data="caseList"
                  border
                  width="100%"
                  @selection-change="caseSelectionChange"
                  :row-key="(row) => row.case_id"
                  ref="caseSelectTable" v-loading="getCaseListLoading">
          <el-table-column type="selection" min-width="55px" :reserve-selection="true"/>
          <el-table-column label="案例ID" prop="case_id" min-width="50px"></el-table-column>
          <el-table-column label="案例名称" prop="case_name"  show-overflow-tooltip></el-table-column>
          <el-table-column v-if="caseQueryInfo.type === 'API'" label="所属接口" prop="interface_id" width="80px" show-overflow-tooltip></el-table-column>
          <el-table-column v-if="caseQueryInfo.type === 'API'" label="接口地址" prop="interface_uri" min-width="200px" show-overflow-tooltip></el-table-column>
          <el-table-column label="修改人" prop="update_person" width="80px"></el-table-column>
          <el-table-column label="修改时间" prop="update_time" width="140px"></el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="caseSizeChange"
          @current-change="caseCurrentChange"
          :current-page.sync="caseQueryInfo.pagenum"
          :page-size="caseQueryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="caseTotal">
        </el-pagination>
      </el-row>
      </el-scrollbar>
      <div class="add-case-drawer-footer">
        <el-button @click="addCasedialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addCase">确 定</el-button>
      </div>
    </el-drawer>
    <!-- 编辑案例抽屉 -->
    <el-drawer :visible.sync="editCasedialogVisible"
               title="编辑案例"
               direction="rtl"
               size="50%"
               class="edit-case-drawer"
               @close="editCaseDrawerClosed">
      <el-scrollbar class="edit-case-drawer-content">
        <el-form :model="caseForm"
                  label-width="100px"
                 :rules="caseFormRules"
                  ref="caseFormRef">
          <el-row>
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="caseForm.relation_case_name" readonly></el-input>
            </el-form-item>
          </el-row>
          <el-row v-if="caseForm.relation_case_type==='API' ||caseForm.relation_case_type==='EXPORT'">
            <el-form-item label="接口地址" prop="case_name">
              <el-input v-model="caseForm.interface_address" readonly></el-input>
            </el-form-item>
          </el-row>
          <el-form-item label="备注说明" prop="case_name">
              <el-input v-model="caseForm.instructions"></el-input>
            </el-form-item>
          <!-- 参数开关，休眠时间 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否提取参数">
                <el-switch
                  v-model="caseForm.is_get_param"
                  active-color="#13ce66">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 返回参数 -->
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="24">
              <el-form-item label="返回参数">
                <el-input v-model="caseForm.response_param"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="24">
              <el-form-item label="返回参数别名">
                <el-input v-model="caseForm.response_param_alias"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 请求参数，头部 -->
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="12">
              <el-form-item label="请求参数">
                <el-input v-model="caseForm.request_param"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="返回头部">
                <el-input v-model="caseForm.response_header_param"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 休眠时间，排序 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="休眠时间">
                <el-input v-model="caseForm.sleep_time">
                  <template slot="append">秒</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 请求数据 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否重设数据">
                <el-tooltip content="开启可编辑请求数据与断言" placement="top" :enterable="true">
                  <el-switch v-model="caseForm.reset_param"
                            active-color="#13ce66">
                  </el-switch>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="模板地址" prop="temp_file" v-if="caseForm.relation_case_type==='UPLOAD'">
              <el-input v-model="caseForm.temp_file" :disabled="caseForm.reset_param === false"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-col :span="5" v-if="caseForm.relation_case_type==='UPLOAD'">
              <el-form-item label="上传行数" prop="upload_min_rows">
                <el-input
                  v-model="caseForm.upload_min_rows"
                  label="最小值"
                  placeholder="最小值"
                  style=" width: 100px"
                  :disabled="caseForm.reset_param === false"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4" v-if="caseForm.relation_case_type==='UPLOAD'">
              <el-form-item label="至">
                <el-tooltip class="item" effect="dark" content="为空表示不限制行数,如最小值=2,最大值为空,则表示上传行数为第2行到最后一行" placement="top">
                  <el-input
                    v-model="caseForm.upload_max_rows"
                    label="最大值"
                    placeholder="最大值"
                    style=" width: 100px"
                    :disabled="caseForm.reset_param === false"
                  ></el-input>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="caseForm.relation_case_type==='UPLOAD'">
            <el-form-item label="文件上传" prop="is_upload">
              <el-input v-model="caseForm.is_upload" v-show="false"></el-input>
              <el-upload
                ref="Files"
                :action="uploadPath"
                :headers="headers"
                class="upload-demo"
                :auto-upload="true"
                :on-change="handleFileChange"
                :before-remove="beforeRemove"
                :on-remove="handleRemove"
                :on-exceed="handleExceed"
                :on-success="handleUploadSuccess"
                :file-list="fileList"
                :limit="1">
                <el-button type="primary" :disabled="caseForm.reset_param === false">选择文件</el-button>
              </el-upload>
            </el-form-item>
          </el-row>
          <el-row v-if="caseForm.relation_case_type !=='EXPORT' && caseForm.relation_case_type !=='UPLOAD'">
            <el-col>
              <el-form-item label="请求数据">
                <el-input type="textarea"
                          :rows="10"
                          placeholder="请输入json数据"
                          v-model="caseForm.interface_data"
                          :disabled="caseForm.reset_param === false"
                          @blur="transferJson">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="caseForm.relation_case_type =='UPLOAD'">
            <el-col>
              <el-form-item label="请求数据">
                <el-input type="textarea"
                          :rows="10"
                          placeholder="请输入json数据"
                          v-model="caseForm.upload_param"
                          :disabled="caseForm.reset_param === false"
                          @blur="transferJson">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 断言 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否断言">
                <el-switch v-model="caseForm.is_assert" @change="handleExportFormIsAssertSwitch"
                          active-color="#13ce66">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        <div v-if="caseForm.is_assert">
          <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="数据匹配"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" :disabled="caseForm.reset_param === false" @click="addMatchAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
          <el-table :data="matchAssertList" border width="60%">
            <el-table-column :label="caseForm.relation_case_type ==='EXPORT' ? '断言字段':'返回值'" min-width="50px">
              <template v-slot="slotProps">
                <el-input v-model="slotProps.row.return_value" :disabled="(caseForm.relation_case_type ==='EXPORT' && slotProps.$index === 0) || caseForm.reset_param === false"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="判断符" min-width="50px">
              <template v-slot="slotProps">
                <el-select v-model="slotProps.row.assert_operator" :disabled="(caseForm.relation_case_type ==='EXPORT' && slotProps.$index === 0) || caseForm.reset_param === false">
                  <el-option value="1" label="等于"></el-option>
                  <el-option value="2" label="不等于"></el-option>
                  <el-option value="3" label="包含"></el-option>
                  <el-option value="4" label="不包含"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column min-width="50px">
              <template v-slot:header>
                <span>预期值</span>
                <el-tooltip  placement="top" :enterable="true">
                  <template #content>
                    <div>
                      <p>目前可支持的内容：</p>
                      <p>1、中文、数字</p>
                      <p>2、变量 ： ${xx}$、${xx}$.xx、${xx}$.xx.xx</p>
                      <p>3、变量四则运算(加减乘除) ： ${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2</p>
                      <p>4、round函数及多个round（加减乘除）：  round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2) - round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2)</p>
                      <p>5、最后结果百分比% ： (round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2) - round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2)) %</p>
                    </div>
                  </template>
                  <el-icon>
                    <i class="el-icon-info" style="font-size: 15px;"></i>
                  </el-icon>
                </el-tooltip>
              </template>
              <template v-slot="slotProps">
                <el-input :disabled="caseForm.reset_param === false"  v-model="slotProps.row.expect_result"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="删除" min-width="50px">
              <template v-slot="slotProps">
                <el-button type="danger" :disabled="(caseForm.relation_case_type ==='EXPORT' && slotProps.$index === 0 && slotProps.row.return_value === 'ROW') || caseForm.reset_param === false"
                          @click="deleteAssert(true, slotProps.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="caseForm.relation_case_type !=='EXPORT' && caseForm.relation_case_type !=='UPLOAD'">
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="四则运算"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" :disabled="caseForm.reset_param === false" @click="addArithmeticAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="caseForm.relation_case_type !=='EXPORT' && caseForm.relation_case_type !=='UPLOAD'">
          <el-form-item>
            <el-table :data="arithmeticAssertList" border width="60%">
              <el-table-column label="被运算值" min-width="50px">
                <template v-slot="slotProps">
                  <el-input :disabled="caseForm.reset_param === false" v-model="slotProps.row.return_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算符" min-width="50px">
                <template v-slot="slotProps">
                  <el-select :disabled="caseForm.reset_param === false" v-model="slotProps.row.assert_operator">
                    <el-option value="1" label="+"></el-option>
                    <el-option value="2" label="-"></el-option>
                    <el-option value="3" label="×"></el-option>
                    <el-option value="4" label="÷"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="运算值" min-width="50px">
                <template v-slot="slotProps">
                  <el-input :disabled="caseForm.reset_param === false" v-model="slotProps.row.operation_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算结果" min-width="50px">
                <template v-slot="slotProps">
                  <el-input :disabled="caseForm.reset_param === false" v-model="slotProps.row.expect_result"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="删除" min-width="50px">
                <template v-slot="slotProps">
                  <el-button type="danger"
                             :disabled="caseForm.reset_param === false"
                            @click="deleteAssert(false, slotProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        </div>
        </el-form>
      </el-scrollbar>
      <div class="edit-case-drawer-footer">
        <el-button @click="editCasedialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="editCase">确 定</el-button>
      </div>
    </el-drawer>
    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                v-loading="loading"
                element-loading-text="场景运行中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="sceneRunForm"
               ref="sceneRunFormRef"
               :rules="sceneRunFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="sceneRunForm.env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSceneRun">确 定</el-button>
      </span>
    </el-dialog>
        <!-- 任务选择对话框 -->
    <el-dialog :visible.sync="taskDialogVisible"
                width="40%"
                :title = "title"
                @close = "taskListClosed">
      <el-form label-width="140px"
               :model="sceneRunForm"
               ref="sceneRunFormRef"
               :rules="sceneRunFormRules">
        <el-form-item label="请选择需添加的任务">
          <el-select v-model="selectedTaskList" multiple filterable>
            <el-option
              v-for="task in taskList"
              :key="task.task_id"
              :label="`${task.task_id}-${task.task_name}`"
              :value="task.task_id">
          </el-option>
          </el-select>
        </el-form-item>
        <template>
          <div>
            <h3>该场景已关联任务</h3>
            <el-table
              :data="selected_tasks"
              style="width: 100%">
              <el-table-column
                prop="task_name"
                label="任务名称"
                width="450">
              </el-table-column>
              <el-table-column
                label="操作"
                width="80">
                <template v-slot="slotProps">
                  <el-button @click="deleteSelectedTask(slotProps.row)" type="text" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="taskListClosed">取 消</el-button>
        <el-button type="primary" @click="submitTaskList">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 展示运行结果对话框 -->
    <el-dialog :visible.sync="resultDialogVisible"
               width="60%"
               :close-on-click-modal="false"
               @close="resultDialogClosed">
      <template slot="title">
        <p style="font-weight: 600;font-size: 16px">{{ resultForm.msg }}</p>
      </template>
      <template v-if="resultForm.code">
        <div v-if="resultForm.code!='0000'" style="color: red;font-size:15px;font-weight: bold;margin-bottom: 10px;">{{resultForm.error}}</div>
        <div v-else style="color: #67C23A;font-size:15px;font-weight: bold;margin-bottom: 10px;">场景执行完成！</div>
      </template>
      <el-collapse>
        <el-collapse-item v-for="(item, index) in resultForm.data" :key="index">
          <template slot="title">
            <span v-if="item.assert==='0'" style="color: red;font-size:15px;">{{ item.title }}</span>
            <span v-else style="font-size:15px;">{{ item.title }}</span>
          </template>
          <template slot="name">
            {{ index }}
          </template>
            <div v-for="(detail, index) in item.detail"
                 style="color: #505050;"
                 :key="index">{{ detail }}</div>
        </el-collapse-item>
        </el-collapse>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="resultDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'

import { baseUrl } from '../../main'

export default {
  data() {
    return {
      model: 'scene_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        search_scene_id: null,
        search_scene_name: '',
        case_id: null,
        creater: window.localStorage.getItem('user_name'),
        business: null,
        pagenum: 1,
        pagesize: 5
      },
      sceneList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      preparamList: [],
      caseParamList: [{}],
      caseList: [],
      sceneForm: {
        scene_name: '',
        business_id: 0,
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      },
      sceneFormRules: {
        scene_name: [
          { required: true, message: '请输入场景名称', trigger: 'blur' }
        ]
      },
      sceneRunFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      isAdd: true,
      enviromentDialogVisible: false,
      sceneRunForm: {
        scene_id: '',
        env_type: '1'
      },
      resultDialogVisible: false,
      resultForm: {},
      loading: false,
      caseDialogVisible: false,
      sqlDialogVisible: false,
      caseForm: {
        temp_file: '',
        upload_max_rows: '',
        upload_min_rows: '',
        upload_param: ''
      },
      sqlForm: {},
      matchAssertList: [],
      arithmeticAssertList: [],
      addCasedialogVisible: false,
      caseQueryInfo: {
        search_case_id: null,
        search_case_name: '',
        search_case_uri: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      },
      caseTotal: 0,
      selectedCaseList: [],
      editCasedialogVisible: false,
      originalIndex: 0,
      addLoading: false,
      addIndex: 0,
      counterKey: 0,
      type: '',
      businessList: [],
      getCaseListLoading: false,
      taskDialogVisible: false,
      selectedTaskList: [],
      taskList: [],
      selected_tasks: [],
      selectedTaskParam: {
        task_id: [],
        scene_id: '',
        scene_name: ''
      },
      fileList: [],
      headers: {
        Authorization: window.localStorage.getItem('token'),
        userId: window.localStorage.getItem('userId')
      },
      caseFormRules: {
        upload_min_rows: [
          { required: true, message: '请输入最小行数', trigger: 'blur' }
        ],
        is_upload: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ],
        temp_file: [
          { required: true, message: '请输入模板下载地址', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    uploadPath() {
      // 动态生成上传路径
      // const baseURl = axios.defaults.baseURL
      return `${baseUrl}user_info/upload_file/`
    }
  },
  created() {
    this.getSceneList()
    this.getCaseList()
    this.getBusinessList()
  },
  watch: {
    'caseForm.upload_min_rows'(newVal) {
      // 最小值不能小于1
      if (newVal < 1) {
        this.caseForm.upload_min_rows = 1
      }
      // 如果最小值大于最大值，将最大值设置为与最小值相等
      if (newVal && this.caseForm.upload_max_rows && newVal > this.caseForm.upload_max_rows) {
        this.caseForm.upload_max_rows = newVal
      }
    },
    'caseForm.upload_max_rows'(newVal) {
      // 最大值不能小于最小值
      if (newVal && newVal < this.caseForm.upload_min_rows) {
        this.caseForm.upload_max_rows = this.caseForm.upload_min_rows
      }
    }
  },
  methods: {
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    queryScene() {
      this.queryInfo.pagenum = 1
      this.getSceneList()
    },
    async getSceneList() {
      const { data: res } = await this.$http.get(this.model + '/scene_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取场景列表失败')
      this.total = res.data.total
      this.sceneList = res.data.scenes
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getSceneList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getSceneList()
    },
    DialogClosed() {
      this.sceneForm = {
        scene_name: '',
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
      this.preparamList = []
      this.caseForm = []
    },
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增场景'
      } else if (this.type === 'update') {
        this.title = '编辑场景'
      } else if (this.type === 'check') {
        this.title = '查看场景'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        await this.getCaseRelationById(id)
      }
      this.dialogVisible = true
    },
    submitScene(flag) {
      this.$refs.sceneFormRef.validate(async valid => {
        if (!valid) return false
        if (this.sceneForm.relation.length === 0) return this.$message.error('请添加至少一个案例')
        this.addLoading = true
        var preparamStr = ''
        this.preparamList.forEach(item => {
          preparamStr = preparamStr + item.paramName + '=' + item.paramValue + ';'
        })
        this.sceneForm.before_param = preparamStr
        let isCheck = false
        let needCheck = false
        this.sceneForm.relation.forEach(item => {
          if (item.is_enable === true && item.relation_case_type === 'API') {
            let url
            try {
              // 只构造 URL 如果是有效的地址
              url = new URL('http:/' + item.relation_interface_address)
            } catch (error) {
              // 如果地址无效，跳过这个项或采取其他措施
              return // 跳过当前项
            }
            const pathSegments = url.pathname.split('/')
            let interfaceMethodName = pathSegments[pathSegments.length - 1] // 获取路径的最后部分
            if (/[?@$]/.test(interfaceMethodName)) {
              interfaceMethodName = item.relation_interface_address.split('/').filter(Boolean).slice(-2, -1)[0]
            }
            if (interfaceMethodName.startsWith('find') || interfaceMethodName.startsWith('get')) {
              needCheck = true
            }
            if (item.is_check === true) {
              isCheck = true
            }
          }
          if (!item.is_get_param) {
            item.response_param = ''
            item.response_param_alias = ''
            item.request_param = ''
            item.response_header_param = ''
          }
        })
        if (needCheck && !isCheck) {
          this.$confirm('当前场景中可能存在数据越权风险的接口，请确认是否已添加数据越权案例!', '提示', {
            confirmButtonText: '无需添加',
            cancelButtonText: '去添加',
            type: 'warning',
            customClass: 'check-notice-dialog'
          }).then(async () => {
            if (this.isAdd) {
              const { data: res } = await this.$http.post(this.model + '/scene_add/', this.sceneForm)
              this.addLoading = false
              if (res.meta.status !== 200) return this.$message.error('新增场景失败')
              this.$message.success('新增场景成功')
            } else {
              const { data: res } = await this.$http.put(this.model + '/' + this.sceneForm.scene_id + '/scene_update/', this.sceneForm)
              this.addLoading = false
              if (res.meta.status !== 200) return this.$message.error('编辑场景失败')
              this.$message.success('编辑场景成功')
              await this.getCaseRelationById(this.sceneForm.scene_id)
            }
            if (flag) {
              this.dialogVisible = false
              await this.getSceneList()
            }
          }).catch(() => {
            this.addLoading = false
            return false
          })
        } else {
          if (this.isAdd) {
            const { data: res } = await this.$http.post(this.model + '/scene_add/', this.sceneForm)
            this.addLoading = false
            if (res.meta.status !== 200) return this.$message.error('新增场景失败')
            this.$message.success('新增场景成功')
          } else {
            const { data: res } = await this.$http.put(this.model + '/' + this.sceneForm.scene_id + '/scene_update/', this.sceneForm)
            this.addLoading = false
            if (res.meta.status !== 200) return this.$message.error('编辑场景失败')
            this.$message.success('编辑场景成功')
            await this.getCaseRelationById(this.sceneForm.scene_id)
          }
          if (flag) {
            this.dialogVisible = false
            await this.getSceneList()
          }
        }
      })
    },
    removeSceneById(id) {
      this.$confirm('此操作将永久删除该场景, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/scene_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error(result.data.meta.msg)
          this.$message.success('删除场景成功')
          this.getSceneList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    addPreparam() {
      this.preparamList.push({})
    },
    deletePreparm(index) {
      this.preparamList.splice(index, 1)
    },
    queryCase() {
      this.caseQueryInfo.pagenum = 1
      this.getCaseList()
    },
    async getCaseList() {
      this.getCaseListLoading = true
      const { data: res } = await this.$http.get('case_info/case_list/', { params: this.caseQueryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取案例列表失败')
      this.caseList = res.data.cases
      this.caseTotal = res.data.total
      this.getCaseListLoading = false
    },
    deleteCurrentCase(index) {
      this.sceneForm.relation.splice(index, 1)
    },
    async copyScene(id) {
      const { data: res } = await this.$http.get('case_info/' + id + '/case_copy/', { params: { type: 'scene' } })
      if (res.meta.status !== 200) return this.$message.error('复制场景失败')
      this.$message.success('复制场景成功')
      this.getSceneList()
    },
    enviromentDialogClosed() {
      this.$refs.sceneRunFormRef.resetFields()
    },
    async showTaskList(sceneId, sceneName) {
      this.title = sceneId.toString() + '-' + sceneName
      const { data: res } = await this.$http.get('task_info/task_list/?channel=3&scene_id=' + sceneId)
      this.taskList = res.data.tasks
      this.selected_tasks = res.data.selected_tasks
      if (res.meta.status !== 200) return this.$message.error('任务列表获取失败')
      this.selectedTaskParam.scene_id = sceneId.toString()
      this.selectedTaskParam.scene_name = sceneName
      this.taskDialogVisible = true
    },
    async deleteSelectedTask(index) {
      this.$confirm('是否确认将场景从任务中移除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data: res } = await this.$http.get('/task_info/delete_task_scene?task_id=' + index.task_id + '&scene_id=' + this.selectedTaskParam.scene_id)
        if (res.meta.status === 200) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          await this.showTaskList(this.selectedTaskParam.scene_id, this.selectedTaskParam.scene_name)
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async submitTaskList() {
      if (this.selectedTaskList.length !== 0) {
        this.selectedTaskParam = { ...this.selectedTaskParam, task_id: this.selectedTaskList }
        const { data: res } = await this.$http.post('task_info/add_scene_to_more_task/', this.selectedTaskParam)
        if (res.meta.status !== 200) return this.$message.error('操作失败')
        this.$message.success('添加成功')
      } else {
        this.$message.success('操作成功')
      }
      this.selectedTaskParam = []
      this.selectedTaskList = []
      this.taskDialogVisible = false
      await this.getSceneList()
    },
    taskListClosed() {
      this.selectedTaskParam = []
      this.selectedTaskList = []
      this.taskDialogVisible = false
    },
    async submitSceneRun() {
      this.loading = true
      const { data: res } = await this.$http.post(this.model + '/scene_run/', this.sceneRunForm)
      if (res.meta.status !== 200) return this.$message.error('调试场景失败')
      this.resultForm = res.data
      this.loading = false
      this.enviromentDialogVisible = false
      this.resultDialogVisible = true
    },
    async showSceneRunDialog(id) {
      this.sceneRunForm.scene_id = id
      this.enviromentDialogVisible = true
    },
    async getSceneDebugLog(id) {
      const { data: res } = await this.$http.get(this.model + '/scene_debug_log/?scene_id=' + id + '&log_type=2')
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.resultForm = res.data
      this.enviromentDialogVisible = false
      this.resultDialogVisible = true
    },
    resultDialogClosed() {
      this.resultForm = {}
    },
    addCaseDrawerShow(index) {
      this.addIndex = index
      this.addCasedialogVisible = true
      this.getCaseList()
    },
    copyCase(caseInfo) {
      const addCaseList = [caseInfo]
      addCaseList.push()
      this.counterKey += 1
      if (this.addIndex === 9999) {
        this.sceneForm.relation.unshift(...addCaseList)
      } else {
        this.sceneForm.relation.splice(this.addIndex + 1, 0, ...addCaseList)
      }
    },
    caseSizeChange(newSize) {
      this.caseQueryInfo.pagesize = newSize
      this.getCaseList()
    },
    caseCurrentChange(newPage) {
      this.caseQueryInfo.pagenum = newPage
      this.getCaseList()
    },
    caseSelectionChange(value) {
      this.selectedCaseList = value
    },
    addCase() {
      const addCaseList = []
      this.selectedCaseList.forEach(item => {
        addCaseList.push(
          {
            relation_case_id: item.case_id,
            relation_case_name: item.case_name,
            interface_address: item.interface_uri,
            relation_interface_address: item.interface_uri,
            instructions: '',
            relation_case_type: item.case_type,
            is_get_param: false,
            request_param: '',
            response_param: '',
            response_param_alias: '',
            response_header_param: '',
            sleep_time: 0,
            reset_param: false,
            interface_data: '',
            is_assert: false,
            asserts: '',
            counterKey: this.counterKey,
            is_enable: true
          }
        )
        this.counterKey += 1
      })
      if (this.addIndex === 9999) {
        this.sceneForm.relation.unshift(...addCaseList)
      } else {
        this.sceneForm.relation.splice(this.addIndex + 1, 0, ...addCaseList)
      }
      this.addCasedialogVisible = false
    },
    addCasedialogClosed() {
      this.$refs.caseSelectTable.clearSelection()
      this.caseQueryInfo = {
        search_case_id: null,
        search_case_name: '',
        search_case_uri: '',
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      }
      this.caseTotal = 0
    },
    deleteCase(index) {
      this.sceneForm.relation.splice(index, 1)
    },
    async editCaseDialogShow(caseInfo, index) {
      this.originalIndex = index
      const objString = JSON.stringify(caseInfo)
      this.caseForm = JSON.parse(objString)
      this.caseForm = {
        ...JSON.parse(objString)
      }
      // Object.assign(this.caseForm, JSON.parse(objString))
      if (!this.caseForm.scene_relation_id) {
        this.caseForm.reset_param = true
      }
      const { data: res } = await this.$http.get('/user_info/get_base_info/', {
        params: {
          id: caseInfo.relation_case_id,
          scene_relation_id: caseInfo.scene_relation_id,
          type: 'API'
        }
      })
      if (res.meta.status !== 200) return this.$message.error('获取案例编辑信息失败')
      if (this.caseForm.relation_case_type === 'UPLOAD' && (this.caseForm.file_path === '' || !this.caseForm.interface_data)) {
        this.caseForm = {
          ...this.caseForm,
          upload_max_rows: '',
          upload_min_rows: '',
          temp_file: ''
        }
        this.caseForm.interface_address = res.data.interface_address
        this.caseForm.upload_max_rows = res.data.upload_max_rows
        this.caseForm.upload_min_rows = res.data.upload_min_rows
        this.caseForm.temp_file = res.data.temp_file
        this.fileList = [{
          name: res.data.file_path.split('/').pop(), // 从文件路径中提取文件名
          url: res.data.file_path, // 文件路径
          status: 'success' // 设置文件状态为成功
        }]
        this.caseForm.file_path = ''
      }
      if (this.caseForm.relation_case_type === 'UPLOAD' && this.caseForm.file_path !== '') {
        this.caseForm = {
          ...this.caseForm
        }
        this.fileList = [{
          name: this.caseForm.file_path.split('/').pop(), // 从文件路径中提取文件名
          url: this.caseForm.file_path, // 文件路径
          status: 'success' // 设置文件状态为成功
        }]
      }
      this.caseForm.is_upload = '1'
      this.caseForm.file_path = ''
      if (this.caseForm.relation_case_type === 'API' || this.caseForm.relation_case_type === 'EXPORT') {
        this.caseForm.interface_address = res.data.interface_address
      }
      if ((this.caseForm.interface_data === '' || !this.caseForm.interface_data) && this.caseForm.relation_case_type !== 'EXPORT') {
        this.caseForm.interface_data = res.data.interface_data
        this.caseForm.is_assert = res.data.is_assert
        this.caseForm.asserts = res.data.asserts
      }
      if (this.caseForm.relation_case_type === 'EXPORT' && this.caseForm.asserts.length === 0) {
        this.caseForm.interface_data = res.data.interface_data
        this.caseForm.is_assert = res.data.is_assert
        this.caseForm.asserts = res.data.asserts
      }
      this.caseForm.asserts.forEach(item => {
        if (item.assert_type === '1') {
          this.matchAssertList.push(item)
        } else if (item.assert_type === '2') {
          this.arithmeticAssertList.push(item)
        }
      })
      this.editCasedialogVisible = true
    },
    transferJson () {
      if (this.caseForm.interface_data && this.caseForm.relation_case_type === 'API') {
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/:\s*@{/g, ':"*@{').replace(/:\s*\[@{/g, ':["*@{').replace(/:\s*{@{/g, ':{"*@{')
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/}@,/g, '}@*",').replace(/}@]/g, '}@*"]').replace(/}@}/g, '}@*"}')
        this.caseForm.interface_data = JSON.parse(this.caseForm.interface_data)
        this.caseForm.interface_data = JSON.stringify(this.caseForm.interface_data, null, 4)
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/"\*/g, '').replace(/\*"/g, '')
      }
    },
    addMatchAssert() {
      this.matchAssertList.push({
        assert_type: '1',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    deleteAssert(isInterface, index) {
      if (isInterface) {
        this.matchAssertList.splice(index, 1)
      } else {
        this.arithmeticAssertList.splice(index, 1)
      }
    },
    editCase() {
      this.$refs.caseFormRef.validate(async valid => {
        if (!valid) return false
        const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
        this.caseForm.asserts = asserts
        if (this.caseForm.is_assert) {
          if (this.caseForm.asserts.length === 0) return this.$message.error('断言为是，请填写断言内容')
          // 判断断言字段是否有重复项
          let flag = false
          this.matchAssertList.forEach(item => {
            if (this.matchAssertList.some(item2 => item !== item2 && item.return_value === item2.return_value)) flag = true
          })
          if (flag) return this.$message.error('断言字段不能有重复项')
        }
        if (this.caseForm.is_upload) {
          if (this.caseForm.is_upload !== '1') return this.$message.error('请上传文件!')
        }
        this.$set(this.sceneForm.relation, this.originalIndex, this.caseForm)
        this.editCasedialogVisible = false
      })
    },
    addArithmeticAssert() {
      this.arithmeticAssertList.push({
        assert_type: '2',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    editCaseDrawerClosed() {
      this.matchAssertList = []
      this.arithmeticAssertList = []
    },
    sceneDrawerClosed() {
      this.sceneForm = {
        scene_name: '',
        business_id: 0,
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
      this.caseForm = []
      this.preparamList = []
    },
    // 案例拖拽排序
    rowDrop() {
      const tbody = document.querySelector('#crTable .el-table__body-wrapper tbody')
      const _this = this
      var ops = {
        animation: 800,
        onEnd: function({ newIndex, oldIndex }) {
          const currRow = _this.sceneForm.relation.splice(oldIndex, 1)[0]
          _this.sceneForm.relation.splice(newIndex, 0, currRow)
        }
      }
      this.$sortable.create(tbody, ops)
    },
    addDrawerOpened() {
      this.rowDrop()
    },
    async getCaseRelationById(id) {
      const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'SCENE' } })
      this.sceneForm = res.data
      const preParaArry = res.data.before_param.split(';')
      if (preParaArry.length > 1) {
        this.preparamList = []
        preParaArry.pop()
        this.$set(this.sceneForm, 'isShowPreparam', true)
        preParaArry.forEach(item => {
          const paramName = item.split('=')[0]
          const paramValue = item.split('=')[1]
          this.preparamList.push(
            {
              paramName: paramName,
              paramValue: paramValue
            }
          )
        })
      }
      this.sceneForm.relation.forEach(item => {
        item.counterKey = this.counterKey
        item.is_update = '0'
        this.counterKey += 1
        if (item.is_check === 1) {
          item.is_check = true
        } else {
          item.is_check = false
        }
      })
      if (res.meta.status !== 200) return this.$message.error('获取场景信息失败')
    },
    handleExportFormIsAssertSwitch(status) {
      if (status && this.matchAssertList.length === 0) {
        this.matchAssertList.push({ assert_type: '1', return_value: 'ROW', assert_operator: '1', expect_result: '', operation_value: '' })
      } else {
        this.matchAssertList = []
      }
    },
    changeIsCheck(caseInfo) {
      const objString = JSON.stringify(caseInfo)
      this.caseForm = JSON.parse(objString)
    },
    handleFileChange(file, fileList) {
      this.caseForm.file = file
    },
    beforeRemove(file, fileList) {
      if (this.caseForm.reset_param === false) {
        this.$message.warning('重设数据关闭无法删除文件!')
        return false // 返回 false 将阻止文件被删除
      }
      return true // 返回 true 允许文件被删除
    },
    handleRemove(file, fileList) {
      this.caseForm.file = null
      this.caseForm.is_upload = '0'
      this.$forceUpdate()
    },
    handleExceed(files, fileList) {
      this.$message.warning('请先删除已上传文件后重新上传!')
    },
    handleUploadSuccess(response, file) {
      this.caseForm.is_update = '1'
      this.caseForm.is_upload = '1'
      this.caseForm.file_path = response.data.file_path
      if (response.meta.status !== 200) return this.$message.error('文件上传失败')
      this.$message.success('文件上传成功！')
      // this.$refs.Files.clearFiles()
    }
  }
}
</script>

<style lang="less">
.cell button {
  margin-left: 5px !important;
  margin-right: 10px !important;
  margin-top: 5px !important;
}
.scene-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
    }
  .el-drawer__header{
    margin-bottom: 20px;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .scene-drawer-content{
    height: 0;
    flex-grow: 2;
  }
  .scene-drawer-footer {
    padding: 20px 0;
    text-align: center;
  }
  .el-scrollbar__view {
    padding:0 30px 15px;
  }
  .relationCaseTabel {
    margin-top: 15px;
    border-top: 1px solid #dfe6ec;
    border-left: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
  }
  .el-divider__text {
    font-size: 15px;
    font-weight: bold;
    color: #606266;
  }
  .el-drawer__header {
    text-align: center;
  }
}
.add-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .add-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
.edit-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
   .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .edit-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
.check-notice-dialog .el-message-box__btns {
  display: flex;
  flex-direction: row-reverse;
}

.check-notice-dialog .el-message-box__btns button:nth-child(1) {
  margin-left: 15px;  /* 增加按钮之间的间距 */
}

.check-notice-dialog .el-message-box__btns button:nth-child(2) {
  margin-left: 20px;
  margin-right: 0;
}
</style>
