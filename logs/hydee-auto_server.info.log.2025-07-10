|hydee-auto_server|**************|6bb721becb2b4095af11ef012deed301|||20250710150548757|Thread-5 (process_request_thread):13061484544|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/login/，请求方式：POST，请求参数为：b'{"username":"liuyanxia","password":"123456"}'
|hydee-auto_server|**************|6bb721becb2b4095af11ef012deed301|||20250710150548765|Thread-5 (process_request_thread):13061484544|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|**************|6bb721becb2b4095af11ef012deed301|||20250710150548765|Thread-5 (process_request_thread):13061484544|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|**************|6bb721becb2b4095af11ef012deed301|||20250710150548806|Thread-5 (process_request_thread):13061484544|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": null, "meta": {"msg": "用户名或密码错误", "status": "HD0002"}, "timestamp": 1752131148806}
|hydee-auto_server|**************|6bb721becb2b4095af11ef012deed301|||20250710150548807|Thread-5 (process_request_thread):13061484544|INFO|userRequestMiddleware.py:143|当前请求耗时：0.04891490936279297 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150605761|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150605762|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607204|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607233|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607233|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607233|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607234|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607234|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607234|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607234|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150607234|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609174|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/login/，请求方式：POST，请求参数为：b'{"username":"liuyanxia","password":"123456"}'
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609175|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609175|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609209|Thread-6 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 登录成功
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609217|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"id": 46, "token": "dc0a231ba44d84115cf694c801930e0d", "user_name": "liuyanxia", "role_id": "0", "chinese_name": "刘艳霞", "staff_no": "3113"}, "meta": {"msg": "登录成功", "status": 200}, "timestamp": 1752131169215}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609224|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:143|当前请求耗时：0.04265999794006348 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609302|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/task_info/get_daily_monitoring_list/，请求方式：POST，请求参数为：b'{"environment":"1","start_date":"2025-6-10","end_date":"2025-7-9","is_pending":"","pagenum":1,"pagesize":40}'
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609303|Thread-10 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/interface_info/interface_traffic_summary/，请求方式：POST，请求参数为：b'{"start_date":"2025-6-10","end_date":"2025-7-9","interface_id":"","interface_name":"","interface_address":"","service":"","top_num":"20","pagenum":1,"pagesize":20}'
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609304|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609312|Thread-12 (process_request_thread):12952186880|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/menu/，请求方式：GET，请求参数为：<QueryDict: {}>
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609314|Thread-10 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609315|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609316|Thread-9 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/user_data/?type=scene&time=quarter，请求方式：GET，请求参数为：<QueryDict: {'type': ['scene'], 'time': ['quarter']}>
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609316|Thread-11 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/task_info/get_daily_monitoring_list/，请求方式：POST，请求参数为：b'{"environment":"3","start_date":"2025-6-10","end_date":"2025-7-9","is_pending":"","pagenum":1,"pagesize":40}'
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609317|Thread-13 (process_request_thread):12969013248|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/index/，请求方式：GET，请求参数为：<QueryDict: {}>
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609317|Thread-12 (process_request_thread):12952186880|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609317|Thread-10 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609319|Thread-9 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609321|Thread-11 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609321|Thread-13 (process_request_thread):12969013248|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609321|Thread-12 (process_request_thread):12952186880|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609324|Thread-9 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609324|Thread-11 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609324|Thread-13 (process_request_thread):12969013248|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609332|Thread-14 (recordSystemLog):12985839616|INFO|commonUtil.py:51|用户 liuyanxia 查询日常监控列表，查询条件：环境=1，日期=2025-6-10-2025-7-9
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609337|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 40, "total": 0, "data": []}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752131169338}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609339|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:143|当前请求耗时：0.0351872444152832 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609339|Thread-15 (recordSystemLog):13002665984|INFO|commonUtil.py:51|用户 liuyanxia 查询生产访问量top接口信息，查询条件：开始时间=2025-6-10, 结束时间=2025-7-9, TOP=20
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609343|Thread-16 (recordSystemLog):13019492352|INFO|commonUtil.py:51|用户 liuyanxia 查询日常监控列表，查询条件：环境=3，日期=2025-6-10-2025-7-9
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609346|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/user_data/?type=scene&time=year，请求方式：GET，请求参数为：<QueryDict: {'type': ['scene'], 'time': ['year']}>
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609348|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609352|Thread-12 (process_request_thread):12952186880|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 1, "authName": "系统配置", "path": "system-configure", "children": [{"id": 7, "authName": "用户管理", "path": "users", "children": []}, {"id": 8, "authName": "角色管理", "path": "roles", "children": []}, {"id": 9, "authName": "权限管理", "path": "rights", "children": []}]}, {"id": 2, "authName": "数据配置", "path": "user-configure", "children": [{"id": 47, "authName": "数据库", "path": "databases", "children": []}, {"id": 48, "authName": "测试参数", "path": "parameters", "children": []}, {"id": 50, "authName": "邮件模板", "path": "emails", "children": []}]}, {"id": 3, "authName": "接口测试", "path": "api-test", "children": [{"id": 16, "authName": "域名管理", "path": "projects", "children": []}, {"id": 17, "authName": "接口管理", "path": "interfaces", "children": []}, {"id": 18, "authName": "案例管理", "path": "api-cases", "children": []}, {"id": 19, "authName": "业务线管理", "path": "businesses", "children": []}, {"id": 54, "authName": "场景管理", "path": "scenes", "children": []}, {"id": 56, "authName": "场景任务", "path": "scene-task", "children": []}, {"id": 57, "authName": "待处理接口管理", "path": "interfaceCompare", "children": []}, {"id": 58, "authName": "接口流量统计", "path": "InterfaceTrafficStatistics", "children": []}]}, {"id": 4, "authName": "自动化监控", "path": "daily-monitoring", "children": [{"id": 66, "authName": "日常监控", "path": "daily-monitoring-list", "children": []}]}, {"id": 6, "authName": "测试助手", "path": "test-helper", "children": [{"id": 43, "authName": "日志列表", "path": "logs", "children": []}, {"id": 44, "authName": "发版列表", "path": "releases", "children": []}, {"id": 51, "authName": "工具列表", "path": "utils", "children": []}, {"id": 52, "authName": "优惠计算器", "path": "money-calculate", "children": []}, {"id": 53, "authName": "请求头转换", "path": "headers", "children": []}]}, {"id": 59, "authName": "鹰眼", "path": "after-sale", "children": [{"id": 60, "authName": "问题列表", "path": "question-list", "children": []}, {"id": 61, "authName": "群聊记录列表", "path": "chatRecord-list", "children": []}, {"id": 62, "authName": "售后问题统计", "path": "question-statistics", "children": []}, {"id": 70, "authName": "数据归档", "path": "data-archiving", "children": []}, {"id": 74, "authName": "群初始化配置", "path": "room-init-config", "children": []}]}, {"id": 71, "authName": "测试用例库", "path": "test-case", "children": [{"id": 63, "authName": "测试用例管理", "path": "testcaseManagement", "children": []}, {"id": 72, "authName": "AI测试用例库", "path": "AI-test-case", "children": []}, {"id": 75, "authName": "AI生成用例", "path": "ai-generate-testcases", "children": []}]}], "meta": {"msg": "获取菜单列表成功", "status": 200}, "timestamp": 1752131169353}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609353|Thread-11 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 40, "total": 0, "data": []}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752131169354}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609354|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609354|Thread-9 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"user_data": [], "count_data": []}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752131169354}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609354|Thread-12 (process_request_thread):12952186880|INFO|userRequestMiddleware.py:143|当前请求耗时：0.04046225547790527 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609355|Thread-11 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.036994218826293945 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609356|Thread-9 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03822183609008789 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609370|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"user_data": ["杨逍", "李明丽"], "count_data": [4, 1]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752131169370}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609370|Thread-5 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:143|当前请求耗时：0.02363872528076172 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609428|Thread-13 (process_request_thread):12969013248|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"test_rate": [], "uat_rate": [], "scene_count": 5210, "yesterday_scene": 5210, "today_scene": 0, "interface_count": 5963, "yesterday_interface": 5963, "today_interface": 0, "case_count": 5856, "yesterday_case": 5856, "today_case": 0, "api_task_count": 64, "yesterday_api_task": 64, "today_api_task": 0, "test_core_scene": 4175, "pro_core_scene": 3250, "interface_coverage_num": 3668}, "meta": {"msg": "操作成功", "status": 200}, "timestamp": 1752131169428}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609429|Thread-13 (process_request_thread):12969013248|INFO|userRequestMiddleware.py:143|当前请求耗时：0.11128091812133789 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609520|Thread-10 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 20, "total": 0, "data": []}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752131169521}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150609521|Thread-10 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.21738696098327637 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611605|Thread-17 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/import_case_report_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611610|Thread-17 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611610|Thread-17 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611614|Thread-18 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611614|Thread-18 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611615|Thread-18 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611624|Thread-19 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询参数信息，查询条件：迭代名称=，业务线= , 创建者= 
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611626|Thread-20 (recordSystemLog):***********|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611635|Thread-18 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752131171635}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611637|Thread-18 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.021227121353149414 秒
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611683|Thread-17 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 19, "data": [{"is_active": true, "id": 44, "business_line": "6", "iteration_id": "", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "requirement_link": "http://c.hydee.cn/pages/viewpage.action?pageId=249483369", "smoke_result_statistics": "", "operator": "胡鹏", "update_time": "2025-06-19T11:06:22", "update_person": "", "businessName": "营销", "total_count": 32, "pass_count": 0}, {"is_active": true, "id": 43, "business_line": "6", "iteration_id": "", "iteration_name": "【V2.4.11.11】海川日常-0603", "requirement_link": "http://c.hydee.cn/pages/viewpage.action?pageId=252710206", "smoke_result_statistics": "", "operator": "胡鹏", "update_time": "2025-06-19T10:53:50", "update_person": "", "businessName": "营销", "total_count": 18, "pass_count": 0}, {"is_active": true, "id": 42, "business_line": "26", "iteration_id": "", "iteration_name": "【V2.3.4.22】商品云_铺货相关", "requirement_link": "https://doc.weixin.qq.com/doc/w3_AKMAQgZ5ALQCN0iOR65tmRImhBAma?scode=AOcA-wd2AAwgHYQFHLAKMAQgZ5ALQ", "smoke_result_statistics": "", "operator": "刘艳霞", "update_time": "2025-06-17T17:10:31", "update_person": "", "businessName": "数据中心", "total_count": 32, "pass_count": 0}, {"is_active": true, "id": 41, "business_line": "26", "iteration_id": "", "iteration_name": "【V2.3.4.22】商品云_铺货相关", "requirement_link": "https://doc.weixin.qq.com/doc/w3_AKMAQgZ5ALQCN0iOR65tmRImhBAma?scode=AOcA-wd2AAwgHYQFHLAKMAQgZ5ALQ", "smoke_result_statistics": "", "operator": "刘艳霞", "update_time": "2025-06-17T17:09:32", "update_person": "", "businessName": "数据中心", "total_count": 0, "pass_count": 0}, {"is_active": true, "id": 40, "business_line": "25", "iteration_id": "", "iteration_name": "【V2.3.4.22】商品云_铺货相关", "requirement_link": "https://doc.weixin.qq.com/doc/w3_AKMAQgZ5ALQCN0iOR65tmRImhBAma?scode=AOcA-wd2AAwgHYQFHLAKMAQgZ5ALQ", "smoke_result_statistics": "", "operator": "刘艳霞", "update_time": "2025-06-17T17:08:45", "update_person": "", "businessName": "云仓", "total_count": 0, "pass_count": 0}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752131171683}
|hydee-auto_server|************|826e865697924d829cf08aec9ee7bcfc|||20250710150611683|Thread-17 (process_request_thread):6302953472|INFO|userRequestMiddleware.py:143|当前请求耗时：0.07823491096496582 秒
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153932580|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153932580|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933205|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933233|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933233|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933233|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933233|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933233|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933234|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933234|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|0a46df6a663245609a1852f448b6d7be|||20250710153933234|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162134867|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162134868|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135357|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135385|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135386|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135386|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135386|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135386|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135386|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135386|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|d6e87f2b9f1e4da7915a961b4e178921|||20250710162135386|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148017|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148017|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148448|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|de785d1e25ff485f85c08fc07e2bcee7|||20250710162148475|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
